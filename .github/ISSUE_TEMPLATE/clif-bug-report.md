---
name: Cranelift Bug Report
about: Report a bug or a crash in Cranelift.
title: 'Crane<PERSON>: '
labels: bug, cranelift
assignees: ''

---

Thanks for filing an issue! Please fill out the TODOs below.

### `.clif` Test Case

```
TODO: paste .clif test case here. Ideally, a test case that has been reduced via
the `clif-util bugpoint` command.
```

### Steps to Reproduce

* TODO: First, ...
* TODO: Then, ...
* Etc...

### Expected Results

TODO: What do you expect to happen?

### Actual Results

TODO: What actually happens? Panic? Segfault? Incorrect result?

### Versions and Environment

Cranelift version or commit: TODO

Operating system: TODO

Architecture: TODO

### Extra Info

Anything else you'd like to add?
