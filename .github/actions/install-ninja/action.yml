name: 'Install ninja'
description: 'Install ninja'

runs:
  using: composite
  steps:
    - name: Install ninja (macOS)
      run: brew install ninja
      if: runner.os == 'macOS'
      shell: bash
    - name: Install ninja (Windows)
      run: choco install ninja
      if: runner.os == 'Windows'
      shell: bash
    - name: Install ninja (Linux)
      run: sudo apt-get update && sudo apt-get install -y ninja-build
      if: runner.os == 'Linux'
      shell: bash
