name: 'Fetch run id for commit'
description: 'Fetch the main.yml run id for the current commit'

runs:
  using: composite
  steps:
    - name: Fetch run id
      shell: bash
      run: |
        run_id=$(
          gh api -H 'Accept: application/vnd.github+json' \
              /repos/${{ github.repository }}/actions/workflows/main.yml/runs\?exclude_pull_requests=true \
              | jq '.workflow_runs' \
              | jq "map(select(.head_commit.id == \"${{ github.sha }}\"))[0].id" \
        )
        echo COMMIT_RUN_ID=${run_id} >> "$GITHUB_ENV"
      env:
        GH_TOKEN: ${{ github.token }}
