name: 'Install Rust toolchain'
description: 'Install a rust toolchain'

inputs:
  toolchain:
    description: 'Default toolchan to install'
    required: false
    default: 'default'
  msrv_range:
    description: 'Versions later-than-latest-Rust the MSRV supports'
    required: false
    default: '2'

runs:
  using: composite
  steps:
    - name: Install Rust
      shell: bash
      id: select
      run: |
        # Determine MSRV as N in `1.N.0` by looking at the `rust-version`
        # located in the root `Cargo.toml`.
        msrv=$(grep 'rust-version.*1' Cargo.toml | sed 's/.*\.\([0-9]*\)\..*/\1/')
        range=${{ inputs.msrv_range }}

        if [ "${{ inputs.toolchain }}" = "default" ]; then
          echo "version=1.$((msrv+range)).0" >> "$GITHUB_OUTPUT"
        elif [ "${{ inputs.toolchain }}" = "msrv" ]; then
          echo "version=1.$msrv.0" >> "$GITHUB_OUTPUT"
        elif [ "${{ inputs.toolchain }}" = "wasmtime-ci-pinned-nightly" ]; then
          echo "version=nightly-2025-08-06" >> "$GITHUB_OUTPUT"
        else
          echo "version=${{ inputs.toolchain }}" >> "$GITHUB_OUTPUT"
        fi

    - name: Install Rust
      shell: bash
      run: |
        rustup set profile minimal
        rustup update "${{ steps.select.outputs.version }}" --no-self-update
        rustup default "${{ steps.select.outputs.version }}"

        # Save disk space by avoiding incremental compilation. Also turn down
        # debuginfo from 2 to 0 to help save disk space.
        cat >> "$GITHUB_ENV" <<EOF
        CARGO_INCREMENTAL=0
        CARGO_PROFILE_DEV_DEBUG=0
        CARGO_PROFILE_TEST_DEBUG=0
        EOF

        # Deny warnings on CI to keep our code warning-free as it lands in-tree.
        echo RUSTFLAGS="-D warnings" >> "$GITHUB_ENV"

        if [[ "${{ runner.os }}" = "macOS" ]]; then
          cat >> "$GITHUB_ENV" <<EOF
        CARGO_PROFILE_DEV_SPLIT_DEBUGINFO=unpacked
        CARGO_PROFILE_TEST_SPLIT_DEBUGINFO=unpacked
        EOF
        fi

    - name: Require semicolons in WIT
      shell: bash
      run: echo WIT_REQUIRE_SEMICOLONS=1 >> "$GITHUB_ENV"

    - name: Install the WASI target
      shell: bash
      run: rustup target add wasm32-wasip1 wasm32-unknown-unknown
