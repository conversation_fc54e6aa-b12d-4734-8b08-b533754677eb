# This is a small action which uses the pre-installed Android NDK on GitHub
# Actions builders, configured with `$ANDROID_NDK`, to compile and link Android
# code. For Rust we mostly need to configure the linker to Cargo and the C
# compiler to the `cc` crate, so this sets various environment variables to the
# appropriate tool within `$ANDROID_NDK`.

name: 'Setup Rust to use the Android NDK'
description: 'Setup Rust to use the android NDK'

inputs:
  target:
    description: 'Rust target being used'
    required: true
  android-platform:
    description: 'Platform version to use for the C compiler'
    required: false
    default: '26'

runs:
  using: composite
  steps:
    - run: |
        target=${{ inputs.target }}
        cc_target=$(echo ${{ inputs.target }} | tr - _)
        upcase=$(echo $target | tr a-z- A-Z_)
        ndk_bin=$ANDROID_NDK/toolchains/llvm/prebuilt/linux-x86_64/bin
        linker=$ndk_bin/${target}${{ inputs.android-platform }}-clang
        echo CARGO_TARGET_${upcase}_LINKER=$linker >> $GITHUB_ENV
        echo CC_${cc_target}=$linker >> $GITHUB_ENV
        echo RANLIB_${cc_target}=$ndk_bin/llvm-ranlib >> $GITHUB_ENV
        echo AR_${cc_target}=$ndk_bin/llvm-ar >> $GITHUB_ENV
      shell: bash
