name: Publish Artifacts
on:
  push:
    branches: [main]
    tags-ignore: [dev]

permissions:
  contents: write
  id-token: write
  attestations: write

jobs:
  publish:
    name: Publish artifacts of build
    runs-on: ubuntu-latest
    if: github.repository == 'bytecodealliance/wasmtime'
    steps:
    - uses: actions/checkout@v4
    - uses: ./.github/actions/fetch-run-id
    - run: |
        gh run download ${COMMIT_RUN_ID}
        ls
        find bins-*
      env:
        GH_TOKEN: ${{ github.token }}

    - run: ./ci/merge-artifacts.sh

    # Deploy the `gh-pages.tar.gz` artifact to the `gh-pages` branch.
    - run: tar xf gh-pages.tar.gz
      working-directory: gh-pages
    - if: github.ref == 'refs/heads/main'
      uses: actions/configure-pages@v5
    - if: github.ref == 'refs/heads/main'
      uses: actions/upload-pages-artifact@v3
      with:
        path: "./gh-pages/gh-pages"

    - uses: actions/attest-build-provenance@v1
      with:
        subject-path: 'dist/*'

    - run: npm install --production
      working-directory: .github/actions/github-release
    - name: Publish Release
      uses: ./.github/actions/github-release
      with:
        files: "dist/*"
        token: ${{ github.token }}
      continue-on-error: true

  # See https://github.com/actions/deploy-pages
  deploy:
    name: Deploy gh-pages artifact
    if: github.ref == 'refs/heads/main'
    needs: publish
    permissions:
      pages: write
      id-token: write
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    steps:
      - id: deployment
        uses: actions/deploy-pages@v4
