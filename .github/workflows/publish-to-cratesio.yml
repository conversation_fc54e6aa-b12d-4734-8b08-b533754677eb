# The purpose of this workflow is to publish the wasmtime workspace of crates
# whenever a wasmtime tag is created. This baiscally boils down to running
# `scripts/publish.rs` at the right time.

name: "Publish to crates.io"

on:
  push:
    tags:
    - 'v*'

jobs:
  publish:
    if: github.repository == 'bytecodealliance/wasmtime'
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        submodules: true
    - run: rustup update stable && rustup default stable
    - run: |
        rustc scripts/publish.rs
        ./publish publish
      env:
        CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
    
    # Manifest and publish the wasi-preview1-component-adapter-provider
    - uses: ./.github/actions/fetch-run-id
    - uses: ./.github/actions/build-adapter-provider
      with:
        run-id: ${{ env.COMMIT_RUN_ID }}
    - run: cargo publish -p wasi-preview1-component-adapter-provider --allow-dirty
      env:
        CARGO_REGISTRY_TOKEN: ${{ secrets.CARGO_REGISTRY_TOKEN }}
